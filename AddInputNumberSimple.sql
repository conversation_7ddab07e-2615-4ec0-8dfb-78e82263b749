-- إضافة عمود رقم الإدخال مباشرة إلى قاعدة البيانات

-- التحقق من وجود العمود أولاً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'FieldVisits' AND COLUMN_NAME = 'InputNumber')
BEGIN
    -- إضافة العمود
    ALTER TABLE FieldVisits ADD InputNumber NVARCHAR(50) NULL;
    PRINT 'تم إضافة عمود InputNumber بنجاح';
    
    -- تحديث البيانات الموجودة
    WITH NumberedVisits AS (
        SELECT Id, 
               ROW_NUMBER() OVER (ORDER BY AddDate, Id) as RowNum
        FROM FieldVisits 
        WHERE InputNumber IS NULL
    )
    UPDATE fv 
    SET InputNumber = 'INP' + FORMAT(nv.RowNum, '000000')
    FROM FieldVisits fv
    INNER JOIN NumberedVisits nv ON fv.Id = nv.Id;
    
    PRINT 'تم تحديث أرقام الإدخال للزيارات الموجودة';
END
ELSE
BEGIN
    PRINT 'عمود InputNumber موجود مسبقاً';
END

PRINT 'تم الانتهاء من جميع التحديثات!';
