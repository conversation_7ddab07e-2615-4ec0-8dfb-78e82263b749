using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Tools
{
    public static class ApplyInputNumberUpdate
    {
        public static async Task<bool> ApplyUpdateAsync()
        {
            try
            {
                var connectionString = DatabaseConfig.GetConnectionString();
                Console.WriteLine($"🔗 الاتصال بقاعدة البيانات...");

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();
                Console.WriteLine("✅ تم الاتصال بقاعدة البيانات بنجاح");

                // قراءة ملف SQL
                var sqlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Scripts", "AddInputNumberColumn.sql");
                if (!File.Exists(sqlFilePath))
                {
                    Console.WriteLine($"❌ لم يتم العثور على ملف SQL: {sqlFilePath}");
                    return false;
                }

                var sqlScript = await File.ReadAllTextAsync(sqlFilePath);
                Console.WriteLine("📄 تم قراءة ملف SQL بنجاح");

                // تطبيق SQL
                using var command = new SqlCommand(sqlScript, connection);
                command.CommandTimeout = 120; // 2 دقيقة

                Console.WriteLine("🔄 تطبيق التحديثات...");
                await command.ExecuteNonQueryAsync();
                Console.WriteLine("✅ تم تطبيق جميع التحديثات بنجاح!");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تطبيق التحديثات: {ex.Message}");
                Console.WriteLine($"📋 التفاصيل: {ex}");
                return false;
            }
        }

        public static async Task Main(string[] args)
        {
            Console.WriteLine("🚀 بدء تطبيق تحديثات رقم الإدخال وإصلاح عقد السائق");
            Console.WriteLine("=".PadRight(60, '='));

            var success = await ApplyUpdateAsync();

            Console.WriteLine("=".PadRight(60, '='));
            if (success)
            {
                Console.WriteLine("🎉 تم تطبيق جميع التحديثات بنجاح!");
            }
            else
            {
                Console.WriteLine("💥 فشل في تطبيق التحديثات!");
            }

            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}
