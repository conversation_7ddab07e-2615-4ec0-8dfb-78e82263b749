-- إضافة عمود رقم الإدخال مباشرة إلى قاعدة البيانات
USE [SFDSYS]
GO

-- التحقق من وجود العمود أولاً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'FieldVisits' AND COLUMN_NAME = 'InputNumber')
BEGIN
    -- إضافة العمود
    ALTER TABLE FieldVisits ADD InputNumber NVARCHAR(50) NULL;
    PRINT 'تم إضافة عمود InputNumber بنجاح';
    
    -- إنشاء فهرس فريد
    CREATE UNIQUE INDEX IX_FieldVisits_InputNumber 
    ON FieldVisits (InputNumber) 
    WHERE InputNumber IS NOT NULL;
    PRINT 'تم إنشاء فهرس InputNumber بنجاح';
    
    -- تحديث البيانات الموجودة
    WITH NumberedVisits AS (
        SELECT Id, 
               ROW_NUMBER() OVER (ORDER BY AddDate, Id) as RowNum
        FROM FieldVisits 
        WHERE InputNumber IS NULL
    )
    UPDATE fv 
    SET InputNumber = 'INP' + FORMAT(nv.RowNum, '000000')
    FROM FieldVisits fv
    INNER JOIN NumberedVisits nv ON fv.Id = nv.Id;
    
    PRINT 'تم تحديث أرقام الإدخال للزيارات الموجودة';
END
ELSE
BEGIN
    PRINT 'عمود InputNumber موجود مسبقاً';
END

-- إصلاح تكرار عقود السائقين إذا وجد
IF EXISTS (
    SELECT DriverContract 
    FROM FieldVisits 
    WHERE DriverContract IS NOT NULL AND DriverContract != ''
    GROUP BY DriverContract 
    HAVING COUNT(*) > 1
)
BEGIN
    PRINT 'تم العثور على عقود سائقين مكررة - جاري الإصلاح...';
    
    WITH DuplicateContracts AS (
        SELECT DriverContract, 
               ROW_NUMBER() OVER (PARTITION BY DriverContract ORDER BY AddDate, Id) as RowNum,
               Id
        FROM FieldVisits 
        WHERE DriverContract IS NOT NULL 
        AND DriverContract != ''
        AND DriverContract IN (
            SELECT DriverContract 
            FROM FieldVisits 
            WHERE DriverContract IS NOT NULL AND DriverContract != ''
            GROUP BY DriverContract 
            HAVING COUNT(*) > 1
        )
    )
    UPDATE fv 
    SET DriverContract = dc.DriverContract + '_' + CAST(dc.RowNum AS NVARCHAR(10))
    FROM FieldVisits fv
    INNER JOIN DuplicateContracts dc ON fv.Id = dc.Id
    WHERE dc.RowNum > 1;
    
    PRINT 'تم إصلاح تكرار عقود السائقين';
END
ELSE
BEGIN
    PRINT 'لا توجد عقود سائقين مكررة';
END

PRINT 'تم الانتهاء من جميع التحديثات!';
