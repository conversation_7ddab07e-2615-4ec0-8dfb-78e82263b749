-- إنشاء قاعدة البيانات
USE master;
GO

-- حذف قاعدة البيانات إذا كانت موجودة
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'SFDSYS')
BEGIN
    ALTER DATABASE [SFDSYS] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE [SFDSYS];
END
GO

-- إنشاء قاعدة البيانات الجديدة
CREATE DATABASE [SFDSYS]
ON (NAME = 'SFDSYS', FILENAME = 'C:\Users\<USER>\Desktop\sys\Data\SFDSYS.mdf')
LOG ON (NAME = 'SFDSYS_Log', FILENAME = 'C:\Users\<USER>\Desktop\sys\Data\SFDSYS_Log.ldf');
GO

-- استخدام قاعدة البيانات الجديدة
USE [SFDSYS];
GO

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول FieldVisits بسيط للاختبار
CREATE TABLE FieldVisits (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    VisitNumber NVARCHAR(50),
    DriverContract NVARCHAR(50),
    AddDate DATETIME2 DEFAULT GETDATE(),
    InputNumber NVARCHAR(50) NULL
);
GO

-- إدراج بعض البيانات التجريبية
INSERT INTO FieldVisits (VisitNumber, DriverContract) VALUES 
('V001', 'C001'),
('V002', 'C002'),
('V003', 'C003');
GO

PRINT 'تم إنشاء قاعدة البيانات والجداول بنجاح!';
