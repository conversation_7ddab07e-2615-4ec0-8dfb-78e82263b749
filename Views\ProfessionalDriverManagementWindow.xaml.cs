using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class ProfessionalDriverManagementWindow : Window
    {
        private readonly DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel _viewModel;

        // خاصية لحفظ السائقين المحددين
        public List<DriverModel> ConfirmedSelectedDrivers { get; private set; } = new List<DriverModel>();

        public ProfessionalDriverManagementWindow()
        {
            InitializeComponent();

            // إنشاء وتعيين ViewModel
            _viewModel = new DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel();
            DataContext = _viewModel;

            // تحميل البيانات
            Loaded += async (s, e) => await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // استخدام الدالة الموجودة في ViewModel لتحميل البيانات
                var refreshMethod = _viewModel.GetType().GetMethod("RefreshDataAsync",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (refreshMethod != null)
                {
                    await (Task)refreshMethod.Invoke(_viewModel, null);
                    System.Diagnostics.Debug.WriteLine($"✅ Loaded {_viewModel.AllDrivers.Count} drivers");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ RefreshDataAsync method not found, loading saved data instead");
                    await _viewModel.LoadSavedDriversData();
                }

                // استعادة الاختيار المحفوظ
                RestoreConfirmedSelection();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error loading drivers: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل بيانات السائقين: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DialogResult = false;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error closing window: {ex.Message}");
                Close();
            }
        }

        private void VehicleFilter_Changed(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is CheckBox checkBox && checkBox.Tag != null)
                {
                    var vehicleType = checkBox.Tag.ToString();
                    var isChecked = checkBox.IsChecked ?? false;

                    System.Diagnostics.Debug.WriteLine($"🚗 Vehicle filter changed: {vehicleType} = {isChecked}");

                    // تطبيق الفلترة
                    ApplyVehicleFilter();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in vehicle filter: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الاختيار عند تغيير التحديد في DataGrid
        /// </summary>
        private void DriversDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تحديث قائمة السائقين المحددين في ViewModel
                if (_viewModel != null)
                {
                    // تحديث قائمة السائقين المحددين
                    UpdateSelectedDrivers();

                    System.Diagnostics.Debug.WriteLine($"📊 Selection updated - Selected: {_viewModel.SelectedDriversCount}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating selection: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث قائمة السائقين المحددين
        /// </summary>
        private void UpdateSelectedDrivers()
        {
            try
            {
                if (_viewModel != null)
                {
                    // مسح القائمة الحالية
                    _viewModel.SelectedDrivers.Clear();

                    // إضافة السائقين المحددين
                    foreach (var driver in _viewModel.AllDrivers.Where(d => d.IsSelected))
                    {
                        _viewModel.SelectedDrivers.Add(driver);
                    }

                    // تحديث الإحصائيات - استخدام reflection لاستدعاء OnPropertyChanged
                    var onPropertyChangedMethod = _viewModel.GetType().GetMethod("OnPropertyChanged",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    onPropertyChangedMethod?.Invoke(_viewModel, new object[] { nameof(_viewModel.SelectedDriversCount) });
                    onPropertyChangedMethod?.Invoke(_viewModel, new object[] { nameof(_viewModel.TotalDriversCount) });
                    onPropertyChangedMethod?.Invoke(_viewModel, new object[] { nameof(_viewModel.AvailableDriversCount) });

                    System.Diagnostics.Debug.WriteLine($"✅ Updated selected drivers: {_viewModel.SelectedDrivers.Count}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating selected drivers: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق فلترة السيارات
        /// </summary>
        private void ApplyVehicleFilter()
        {
            try
            {
                // الحصول على أنواع السيارات المحددة
                var selectedVehicleTypes = new List<string>();

                // فحص كل checkbox بالاسم
                if (ForshanalFilter.IsChecked == true) selectedVehicleTypes.Add("فورشنال");
                if (KanterFilter.IsChecked == true) selectedVehicleTypes.Add("كنتر");
                if (HiluxFilter.IsChecked == true) selectedVehicleTypes.Add("هيلوكس");
                if (BusFilter.IsChecked == true) selectedVehicleTypes.Add("حافلة");
                if (PradoFilter.IsChecked == true) selectedVehicleTypes.Add("برادو");

                // تطبيق الفلترة على ViewModel
                if (_viewModel != null)
                {
                    _viewModel.SelectedVehicleTypes = selectedVehicleTypes;
                    System.Diagnostics.Debug.WriteLine($"✅ Applied vehicle filter: {string.Join(", ", selectedVehicleTypes)}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error applying vehicle filter: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن عناصر فرعية من نوع معين
        /// </summary>
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }

        /// <summary>
        /// مسح نص البحث
        /// </summary>
        private void ClearSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _viewModel.SearchText = "";
                System.Diagnostics.Debug.WriteLine("✅ Search text cleared");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error clearing search: {ex.Message}");
            }
        }

        private void SelectAllDrivers_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _viewModel.SelectAllCommand?.Execute(null);
                System.Diagnostics.Debug.WriteLine($"✅ Selected all drivers - Total: {_viewModel.SelectedDrivers.Count}");
                MessageBox.Show($"تم تحديد جميع السائقين ({_viewModel.FilteredDrivers.Count} سائق)", "تم التحديد",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error selecting all drivers: {ex.Message}");
                MessageBox.Show($"خطأ في تحديد السائقين: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearSelection_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _viewModel.ClearSelectionCommand?.Execute(null);
                System.Diagnostics.Debug.WriteLine("✅ Cleared all selections");
                MessageBox.Show("تم إلغاء تحديد جميع السائقين", "تم الإلغاء",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error clearing selections: {ex.Message}");
                MessageBox.Show($"خطأ في إلغاء التحديد: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadDataAsync();
                System.Diagnostics.Debug.WriteLine($"✅ Data refreshed - Total drivers: {_viewModel.AllDrivers.Count}");
                MessageBox.Show($"تم تحديث البيانات بنجاح\nإجمالي السائقين: {_viewModel.AllDrivers.Count}", "تم التحديث",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error refreshing data: {ex.Message}");
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// استعادة الاختيار المحفوظ
        /// </summary>
        private void RestoreConfirmedSelection()
        {
            try
            {
                if (ConfirmedSelectedDrivers?.Count > 0 && _viewModel != null)
                {
                    foreach (var confirmedDriver in ConfirmedSelectedDrivers)
                    {
                        var driver = _viewModel.AllDrivers.FirstOrDefault(d => d.DriverCode == confirmedDriver.DriverCode);
                        if (driver != null)
                        {
                            driver.IsSelected = true;
                        }
                    }

                    UpdateSelectedDrivers();
                    System.Diagnostics.Debug.WriteLine($"✅ Restored {ConfirmedSelectedDrivers.Count} confirmed selections");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error restoring confirmed selection: {ex.Message}");
            }
        }

        private async void ConfirmSelectionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحديث قائمة السائقين المحددين أولاً
                UpdateSelectedDrivers();

                var selectedDrivers = _viewModel.SelectedDrivers;
                if (selectedDrivers?.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار سائق واحد على الأقل", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل تريد فتح نافذة الرسائل للسائقين المحددين؟\n\nعدد السائقين المحددين: {selectedDrivers.Count}",
                                           "تأكيد الاختيار", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ Opening messages window with {selectedDrivers.Count} selected drivers");

                    // حفظ السائقين المحددين
                    ConfirmedSelectedDrivers.Clear();
                    ConfirmedSelectedDrivers.AddRange(selectedDrivers.ToList());

                    // فتح نافذة الرسائل مع السائقين المحددين
                    var messagesWindow = new SFDSystem.Views.ProfessionalMessagesWindow();
                    if (messagesWindow.DataContext is DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel messagesViewModel)
                    {
                        // نسخ السائقين المحددين إلى نافذة الرسائل
                        messagesViewModel.AllDrivers.Clear();
                        foreach (var driver in selectedDrivers)
                        {
                            messagesViewModel.AllDrivers.Add(driver);
                            driver.IsSelected = true;
                        }
                        messagesViewModel.FilterDrivers();
                    }

                    messagesWindow.ShowDialog();

                    // تحديث البيانات بعد إغلاق نافذة الرسائل
                    await LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error confirming selection: {ex.Message}");
                MessageBox.Show($"خطأ في تأكيد الاختيار: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DialogResult = false;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error canceling: {ex.Message}");
                Close();
            }
        }

        /// <summary>
        /// اتصال بالسائق
        /// </summary>
        private void CallDriver_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedDriver = DriversDataGrid.SelectedItem as DriverManagementSystem.ViewModels.DriverModel;
                if (selectedDriver != null && !string.IsNullOrEmpty(selectedDriver.PhoneNumber))
                {
                    var result = MessageBox.Show($"هل تريد الاتصال بالسائق {selectedDriver.Name}؟\nرقم الهاتف: {selectedDriver.PhoneNumber}",
                                               "تأكيد الاتصال", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // فتح تطبيق الهاتف
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = $"tel:{selectedDriver.PhoneNumber}",
                            UseShellExecute = true
                        });
                    }
                }
                else
                {
                    MessageBox.Show("لا يوجد رقم هاتف متاح لهذا السائق", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error calling driver: {ex.Message}");
                MessageBox.Show($"خطأ في الاتصال: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إرسال رسالة للسائق
        /// </summary>
        private void MessageDriver_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedDriver = DriversDataGrid.SelectedItem as DriverManagementSystem.ViewModels.DriverModel;
                if (selectedDriver != null)
                {
                    // تحديد السائق وفتح نافذة الرسائل
                    selectedDriver.IsSelected = true;

                    var messagesWindow = new SFDSystem.Views.ProfessionalMessagesWindow();
                    if (messagesWindow.DataContext is DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel messagesViewModel)
                    {
                        // إضافة السائق المحدد فقط
                        messagesViewModel.AllDrivers.Clear();
                        messagesViewModel.AllDrivers.Add(selectedDriver);
                        messagesViewModel.FilterDrivers();
                    }

                    messagesWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار سائق أولاً", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error messaging driver: {ex.Message}");
                MessageBox.Show($"خطأ في إرسال الرسالة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
