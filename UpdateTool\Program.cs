using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;

namespace UpdateTool;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🚀 تطبيق تحديث رقم الإدخال وإصلاح عقد السائق");
        Console.WriteLine("=".PadRight(60, '='));

        try
        {
            // قراءة connection string من ملف التكوين
            var connectionString = GetConnectionString();
            
            Console.WriteLine("🔗 الاتصال بقاعدة البيانات...");
            
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();
            Console.WriteLine("✅ تم الاتصال بقاعدة البيانات بنجاح");

            // قراءة ملف SQL
            var sqlFilePath = "../AddInputNumberSimple.sql";
            if (!File.Exists(sqlFilePath))
            {
                Console.WriteLine($"❌ لم يتم العثور على ملف SQL: {sqlFilePath}");
                return;
            }

            var sqlScript = await File.ReadAllTextAsync(sqlFilePath);
            Console.WriteLine("📄 تم قراءة ملف SQL بنجاح");

            // تطبيق SQL
            using var command = new SqlCommand(sqlScript, connection);
            command.CommandTimeout = 120; // 2 دقيقة

            Console.WriteLine("🔄 تطبيق التحديثات...");
            await command.ExecuteNonQueryAsync();
            Console.WriteLine("✅ تم تطبيق جميع التحديثات بنجاح!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في تطبيق التحديثات: {ex.Message}");
            Console.WriteLine($"📋 التفاصيل: {ex}");
        }

        Console.WriteLine("=".PadRight(60, '='));
        Console.WriteLine("اضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }

    static string GetConnectionString()
    {
        try
        {
            var configFilePath = Path.Combine("..", "Data", "db_config.txt");
            if (!File.Exists(configFilePath))
            {
                // إعدادات افتراضية
                return @"Server=(LocalDB)\MSSQLLocalDB;Database=SFDSYS;Trusted_Connection=True;MultipleActiveResultSets=True;Connection Timeout=30;";
            }

            var settings = new DatabaseSettings();
            var lines = File.ReadAllLines(configFilePath);

            foreach (var line in lines)
            {
                if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                    continue;

                var parts = line.Split('=', 2);
                if (parts.Length == 2)
                {
                    var key = parts[0].Trim();
                    var value = parts[1].Trim();

                    switch (key)
                    {
                        case "ServerName":
                            settings.ServerName = value;
                            break;
                        case "DatabaseName":
                            settings.DatabaseName = value;
                            break;
                        case "UseWindowsAuth":
                            settings.UseWindowsAuth = bool.Parse(value);
                            break;
                        case "Username":
                            settings.Username = value;
                            break;
                        case "Password":
                            settings.Password = value;
                            break;
                        case "UseLocalDatabase":
                            settings.UseLocalDatabase = bool.Parse(value);
                            break;
                        case "LocalDatabasePath":
                            settings.LocalDatabasePath = value;
                            break;
                    }
                }
            }

            // إذا كانت قاعدة البيانات محلية
            if (settings.UseLocalDatabase)
            {
                return $@"Server=(LocalDB)\MSSQLLocalDB;Database={settings.DatabaseName};Trusted_Connection=True;MultipleActiveResultSets=True;Connection Timeout=30;";
            }

            // قاعدة بيانات خادم عادية
            if (settings.UseWindowsAuth)
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};Trusted_Connection=true;TrustServerCertificate=true;";
            }
            else
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};User Id={settings.Username};Password={settings.Password};TrustServerCertificate=true;";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في قراءة إعدادات قاعدة البيانات: {ex.Message}");
            // إعدادات افتراضية
            return @"Server=(LocalDB)\MSSQLLocalDB;Database=SFDSYS;Trusted_Connection=True;MultipleActiveResultSets=True;Connection Timeout=30;";
        }
    }

    public class DatabaseSettings
    {
        public string ServerName { get; set; } = "localhost";
        public string DatabaseName { get; set; } = "SFDSYS";
        public bool UseWindowsAuth { get; set; } = true;
        public string Username { get; set; } = "sa";
        public string Password { get; set; } = "";
        public bool UseLocalDatabase { get; set; } = true;
        public string LocalDatabasePath { get; set; } = "";
    }
}
