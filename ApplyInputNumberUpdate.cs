using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;

namespace UpdateTool;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🚀 تطبيق تحديث رقم الإدخال وإصلاح عقد السائق");
        Console.WriteLine("=".PadRight(60, '='));

        try
        {
            // قراءة connection string من ملف التكوين
            var connectionString = "Server=(localdb)\\mssqllocaldb;Database=SFDSystem;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true";
            
            Console.WriteLine("🔗 الاتصال بقاعدة البيانات...");
            
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();
            Console.WriteLine("✅ تم الاتصال بقاعدة البيانات بنجاح");

            // قراءة ملف SQL
            var sqlFilePath = "AddInputNumberDirectly.sql";
            if (!File.Exists(sqlFilePath))
            {
                Console.WriteLine($"❌ لم يتم العثور على ملف SQL: {sqlFilePath}");
                return;
            }

            var sqlScript = await File.ReadAllTextAsync(sqlFilePath);
            Console.WriteLine("📄 تم قراءة ملف SQL بنجاح");

            // تطبيق SQL
            using var command = new SqlCommand(sqlScript, connection);
            command.CommandTimeout = 120; // 2 دقيقة

            Console.WriteLine("🔄 تطبيق التحديثات...");
            await command.ExecuteNonQueryAsync();
            Console.WriteLine("✅ تم تطبيق جميع التحديثات بنجاح!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في تطبيق التحديثات: {ex.Message}");
            Console.WriteLine($"📋 التفاصيل: {ex}");
        }

        Console.WriteLine("=".PadRight(60, '='));
        Console.WriteLine("اضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }
}
