using Microsoft.EntityFrameworkCore.Migrations;

namespace SFDSystem.Migrations
{
    /// <summary>
    /// إضافة عمود رقم الإدخال للزيارات الميدانية وإصلاح تكرار عقد السائق
    /// </summary>
    public partial class AddInputNumberColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // إضافة عمود رقم الإدخال
            migrationBuilder.AddColumn<string>(
                name: "InputNumber",
                table: "FieldVisits",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            // إنشاء فهرس فريد لرقم الإدخال
            migrationBuilder.CreateIndex(
                name: "IX_FieldVisits_InputNumber",
                table: "FieldVisits",
                column: "InputNumber",
                unique: true,
                filter: "[InputNumber] IS NOT NULL");

            // إنشاء فهرس فريد لعقد السائق لمنع التكرار
            migrationBuilder.CreateIndex(
                name: "IX_FieldVisits_DriverContract",
                table: "FieldVisits",
                column: "DriverContract",
                unique: true,
                filter: "[DriverContract] IS NOT NULL AND [DriverContract] != ''");

            // تحديث البيانات الموجودة لإضافة أرقام إدخال فريدة
            migrationBuilder.Sql(@"
                -- تحديث أرقام الإدخال للزيارات الموجودة
                WITH NumberedVisits AS (
                    SELECT Id, 
                           ROW_NUMBER() OVER (ORDER BY AddDate, Id) as RowNum
                    FROM FieldVisits 
                    WHERE InputNumber IS NULL
                )
                UPDATE fv 
                SET InputNumber = 'INP' + FORMAT(nv.RowNum, '000000')
                FROM FieldVisits fv
                INNER JOIN NumberedVisits nv ON fv.Id = nv.Id;
            ");

            // إصلاح تكرار عقود السائقين
            migrationBuilder.Sql(@"
                -- إصلاح عقود السائقين المكررة
                WITH DuplicateContracts AS (
                    SELECT DriverContract, 
                           ROW_NUMBER() OVER (PARTITION BY DriverContract ORDER BY AddDate, Id) as RowNum,
                           Id
                    FROM FieldVisits 
                    WHERE DriverContract IS NOT NULL 
                    AND DriverContract != ''
                    AND DriverContract IN (
                        SELECT DriverContract 
                        FROM FieldVisits 
                        WHERE DriverContract IS NOT NULL AND DriverContract != ''
                        GROUP BY DriverContract 
                        HAVING COUNT(*) > 1
                    )
                )
                UPDATE fv 
                SET DriverContract = dc.DriverContract + '_' + CAST(dc.RowNum AS NVARCHAR(10))
                FROM FieldVisits fv
                INNER JOIN DuplicateContracts dc ON fv.Id = dc.Id
                WHERE dc.RowNum > 1;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // حذف الفهارس
            migrationBuilder.DropIndex(
                name: "IX_FieldVisits_InputNumber",
                table: "FieldVisits");

            migrationBuilder.DropIndex(
                name: "IX_FieldVisits_DriverContract",
                table: "FieldVisits");

            // حذف العمود
            migrationBuilder.DropColumn(
                name: "InputNumber",
                table: "FieldVisits");
        }
    }
}
