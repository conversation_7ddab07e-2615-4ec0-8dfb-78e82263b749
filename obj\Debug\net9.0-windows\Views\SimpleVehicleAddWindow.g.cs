﻿#pragma checksum "..\..\..\..\Views\SimpleVehicleAddWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FA33925BC5FBCB9518D31EB2DDFEA7B057781B8E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// SimpleVehicleAddWindow
    /// </summary>
    public partial class SimpleVehicleAddWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 49 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DriverNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CardNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CardTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox VehicleTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VehicleNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VehicleModelTextBox;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox VehicleCapacityComboBox;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox VehicleColorComboBox;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LicenseNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearButton;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/simplevehicleaddwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DriverNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.PhoneNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CardNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.CardTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.VehicleTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.VehicleNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.VehicleModelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.VehicleCapacityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.VehicleColorComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.LicenseNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 163 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ClearButton = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
            this.ClearButton.Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 173 "..\..\..\..\Views\SimpleVehicleAddWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

