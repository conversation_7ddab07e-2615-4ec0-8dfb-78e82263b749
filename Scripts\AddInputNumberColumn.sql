-- إضافة عمود رقم الإدخال للزيارات الميدانية
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'FieldVisits' AND COLUMN_NAME = 'InputNumber')
BEGIN
    ALTER TABLE FieldVisits ADD InputNumber NVARCHAR(50) NULL;
    PRINT 'تم إضافة عمود InputNumber بنجاح';
END
ELSE
BEGIN
    PRINT 'عمود InputNumber موجود مسبقاً';
END

-- إنشاء فهرس فريد لرقم الإدخال
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_FieldVisits_InputNumber')
BEGIN
    CREATE UNIQUE INDEX IX_FieldVisits_InputNumber 
    ON FieldVisits (InputNumber) 
    WHERE InputNumber IS NOT NULL;
    PRINT 'تم إنشاء فهرس InputNumber بنجاح';
END
ELSE
BEGIN
    PRINT 'فهرس InputNumber موجود مسبقاً';
END

-- إنشاء فهرس فريد لعقد السائق لمنع التكرار
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_FieldVisits_DriverContract')
BEGIN
    CREATE UNIQUE INDEX IX_FieldVisits_DriverContract 
    ON FieldVisits (DriverContract) 
    WHERE DriverContract IS NOT NULL AND DriverContract != '';
    PRINT 'تم إنشاء فهرس DriverContract بنجاح';
END
ELSE
BEGIN
    PRINT 'فهرس DriverContract موجود مسبقاً';
END

-- تحديث أرقام الإدخال للزيارات الموجودة
WITH NumberedVisits AS (
    SELECT Id, 
           ROW_NUMBER() OVER (ORDER BY AddDate, Id) as RowNum
    FROM FieldVisits 
    WHERE InputNumber IS NULL
)
UPDATE fv 
SET InputNumber = 'INP' + FORMAT(nv.RowNum, '000000')
FROM FieldVisits fv
INNER JOIN NumberedVisits nv ON fv.Id = nv.Id;

PRINT 'تم تحديث أرقام الإدخال للزيارات الموجودة';

-- إصلاح تكرار عقود السائقين
WITH DuplicateContracts AS (
    SELECT DriverContract, 
           ROW_NUMBER() OVER (PARTITION BY DriverContract ORDER BY AddDate, Id) as RowNum,
           Id
    FROM FieldVisits 
    WHERE DriverContract IS NOT NULL 
    AND DriverContract != ''
    AND DriverContract IN (
        SELECT DriverContract 
        FROM FieldVisits 
        WHERE DriverContract IS NOT NULL AND DriverContract != ''
        GROUP BY DriverContract 
        HAVING COUNT(*) > 1
    )
)
UPDATE fv 
SET DriverContract = dc.DriverContract + '_' + CAST(dc.RowNum AS NVARCHAR(10))
FROM FieldVisits fv
INNER JOIN DuplicateContracts dc ON fv.Id = dc.Id
WHERE dc.RowNum > 1;

PRINT 'تم إصلاح تكرار عقود السائقين';

PRINT 'تم تطبيق جميع التحديثات بنجاح!';
