using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Text;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using System.Windows.Threading;

namespace DriverManagementSystem.ViewModels
{
    public class DashboardViewModel : BindableBase
    {
        // Event to notify other view‑models that the database has been refreshed
        public static event Action? DatabaseRefreshed;
        private DataService _dataService;
        private int _sectorsCount;
        private int _driversCount;
        private int _vehiclesCount;
        private int _projectsCount;
        private int _fieldVisitsCount;

        public DashboardViewModel()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📊 بدء إنشاء DashboardViewModel");

                _dataService = new DataService();
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء DataService");

                InitializeQuickActions();
                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة الإجراءات السريعة");

                // تحميل الإحصائيات بشكل آمن (بدون انتظار)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await LoadStatisticsAsync();
                        System.Diagnostics.Debug.WriteLine("✅ تم تحميل الإحصائيات بنجاح");
                    }
                    catch (Exception statsEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تحميل الإحصائيات: {statsEx.Message}");
                    }
                });
                System.Diagnostics.Debug.WriteLine("🔄 بدء تحميل الإحصائيات (في الخلفية)");

                // إضافة تحديث دوري للإحصائيات (مُعطّل مؤقتاً للاختبار)
                // var timer = new System.Windows.Threading.DispatcherTimer();
                // timer.Interval = TimeSpan.FromSeconds(3);
                // timer.Tick += async (s, e) => await RefreshStatisticsAsync();
                // timer.Start();
                System.Diagnostics.Debug.WriteLine("⏰ تم تعطيل مؤقت التحديث مؤقتاً للاختبار");

                // الاستماع لأحداث تحديث البيانات من ViewModels أخرى
                DropDataViewModel.FieldVisitAdded += OnFieldVisitAdded;
                System.Diagnostics.Debug.WriteLine("🔔 تم تسجيل أحداث التحديث");

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء DashboardViewModel بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء DashboardViewModel: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");

                // إنشاء DataService بديل في حالة الخطأ
                try
                {
                    _dataService = new DataService();
                    InitializeQuickActions();
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في إنشاء DataService البديل: {fallbackEx.Message}");
                    throw; // إعادة رمي الخطأ إذا فشل كل شيء
                }
            }
        }

        public ObservableCollection<QuickAction> QuickActions { get; set; } = new ObservableCollection<QuickAction>();
        public ObservableCollection<StatisticCard> Statistics { get; set; } = new ObservableCollection<StatisticCard>();

        public DelegateCommand<string> QuickActionCommand => new DelegateCommand<string>(ExecuteQuickAction);
        public DelegateCommand RefreshDatabaseCommand => new DelegateCommand(RefreshDatabase);
        public DelegateCommand AddRealDataCommand => new DelegateCommand(AddRealData);
        public DelegateCommand OpenOffersCommand => new DelegateCommand(ExecuteOpenOffers);

        #region Statistics Properties
        public int SectorsCount
        {
            get => _sectorsCount;
            set => SetProperty(ref _sectorsCount, value);
        }

        public int DriversCount
        {
            get => _driversCount;
            set => SetProperty(ref _driversCount, value);
        }

        public int VehiclesCount
        {
            get => _vehiclesCount;
            set => SetProperty(ref _vehiclesCount, value);
        }

        public int ProjectsCount
        {
            get => _projectsCount;
            set => SetProperty(ref _projectsCount, value);
        }

        public int FieldVisitsCount
        {
            get => _fieldVisitsCount;
            set => SetProperty(ref _fieldVisitsCount, value);
        }
        #endregion

        private void InitializeQuickActions()
        {
            QuickActions.Add(new QuickAction { Title = "إضافة قطاع", Icon = "🏢", Action = "AddSector" });
            QuickActions.Add(new QuickAction { Title = "إضافة مشروع", Icon = "📋", Action = "AddProject" });
            QuickActions.Add(new QuickAction { Title = "إضافة سيارة", Icon = "🚗", Action = "AddVehicle" });
            QuickActions.Add(new QuickAction { Title = "بيانات الزيارة", Icon = "🗂️", Action = "FieldVisit" });
            QuickActions.Add(new QuickAction { Title = "عروض الأسعار", Icon = "🏆", Action = "Offers" });
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📊 بدء تحميل الإحصائيات");

                // Load all statistics
                System.Diagnostics.Debug.WriteLine("🔄 تحميل القطاعات");
                var sectors = await _dataService.GetSectorsAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {sectors.Count} قطاع");

                System.Diagnostics.Debug.WriteLine("🔄 تحميل السائقين");
                var drivers = await _dataService.GetDriversAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {drivers.Count} سائق");

                System.Diagnostics.Debug.WriteLine("🔄 تحميل السيارات");
                var vehicles = await _dataService.GetVehiclesAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {vehicles.Count} سيارة");

                System.Diagnostics.Debug.WriteLine("🔄 تحميل المشاريع");
                var projects = await _dataService.GetProjectsAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {projects.Count} مشروع");

                System.Diagnostics.Debug.WriteLine("🔄 تحميل الزيارات الميدانية");
                var fieldVisits = await _dataService.GetFieldVisitsAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {fieldVisits.Count} زيارة");

                // Update counts
                SectorsCount = sectors.Count;
                DriversCount = drivers.Count;
                VehiclesCount = vehicles.Count;
                ProjectsCount = projects.Count;
                FieldVisitsCount = fieldVisits.Count;

                // Initialize statistics cards
                System.Diagnostics.Debug.WriteLine("🔄 تهيئة بطاقات الإحصائيات");
                InitializeStatistics();
                System.Diagnostics.Debug.WriteLine("✅ تم تحميل جميع الإحصائيات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الإحصائيات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");

                // تعيين قيم افتراضية في حالة الخطأ
                SectorsCount = 0;
                DriversCount = 0;
                VehiclesCount = 0;
                ProjectsCount = 0;
                FieldVisitsCount = 0;

                try
                {
                    InitializeStatistics();
                }
                catch (Exception initEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة الإحصائيات الافتراضية: {initEx.Message}");
                }
            }
        }

        private void InitializeStatistics()
        {
            Statistics.Clear();
            Statistics.Add(new StatisticCard { Title = "القطاعات", Count = SectorsCount, Icon = "🏢", Color = "#2196F3" });
            Statistics.Add(new StatisticCard { Title = "السائقين", Count = DriversCount, Icon = "👤", Color = "#4CAF50" });
            Statistics.Add(new StatisticCard { Title = "السيارات", Count = VehiclesCount, Icon = "🚗", Color = "#FF9800" });
            Statistics.Add(new StatisticCard { Title = "المشاريع", Count = ProjectsCount, Icon = "📋", Color = "#9C27B0" });
            Statistics.Add(new StatisticCard { Title = "الزيارات الميدانية", Count = FieldVisitsCount, Icon = "🗂️", Color = "#F44336" });
        }

        private async Task RefreshStatisticsAsync()
        {
            try
            {
                await LoadStatisticsAsync();
                System.Diagnostics.Debug.WriteLine($"📊 تم تحديث الإحصائيات: القطاعات={SectorsCount}, السائقين={DriversCount}, السيارات={VehiclesCount}, المشاريع={ProjectsCount}, الزيارات={FieldVisitsCount}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج حدث إضافة زيارة جديدة
        /// </summary>
        private async void OnFieldVisitAdded()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔔 تم استلام إشعار بإضافة زيارة جديدة - تحديث الإحصائيات");
                await RefreshStatisticsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة إضافة زيارة جديدة: {ex.Message}");
            }
        }

        private void ExecuteQuickAction(string action)
        {
            try
            {
                // Handle quick actions with simple messages for now
                switch (action)
                {
                    case "AddSector":
                        OpenSectorManagement();
                        break;
                    case "AddProject":
                        OpenAddProject();
                        break;
                    case "AddVehicle":
                        OpenVehicleManagement();
                        break;
                    case "FieldVisit":
                        OpenFieldVisit();
                        break;
                    case "Offers":
                        ExecuteOpenOffers();
                        break;
                    default:
                        System.Windows.MessageBox.Show("إجراء غير معروف", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void OpenSectorManagement()
        {
            try
            {
                var sectorWindow = new Views.SectorManagementWindow();
                sectorWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة القطاعات: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void OpenAddProject()
        {
            try
            {
                var addProjectWindow = new Views.AddProjectWindow();

                // Set owner safely
                try
                {
                    if (System.Windows.Application.Current.MainWindow != null)
                    {
                    }
                }
                catch
                {
                    // If setting owner fails, continue without it
                }

                addProjectWindow.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;
                var result = addProjectWindow.ShowDialog();

                if (result == true)
                {
                    System.Windows.MessageBox.Show("✅ تم إضافة المشروع بنجاح!", "نجح الحفظ",
                                   System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة المشروع: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void OpenVehicleManagement()
        {
            try
            {
                // استخدام النافذة المبسطة لإضافة السيارة
                var addVehicleWindow = new Views.SimpleVehicleAddWindow();
                addVehicleWindow.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;

                var result = addVehicleWindow.ShowDialog();

                if (result == true)
                {
                    System.Windows.MessageBox.Show("✅ تم إضافة السيارة والسائق بنجاح!", "نجح الحفظ",
                                   System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                    // تحديث الإحصائيات
                    await LoadStatisticsAsync();
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة السيارة: {ex.Message}", "خطأ",
                                             System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void OpenFieldVisit()
        {
            try
            {
                // Open Field Visit window directly
                // تم حذف نافذة الزيارات الميدانية - يمكن إضافة نافذة بديلة لاحقاً
                System.Windows.MessageBox.Show("هذه الميزة غير متاحة حالياً", "تنبيه", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة الزيارات الميدانية: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void RefreshDatabase()
        {
            try
            {
                // Show loading message
                var loadingWindow = System.Windows.MessageBox.Show(
                    "🔄 جاري فحص وتحديث قاعدة البيانات...\n\n" +
                    "• فحص القطاعات والموظفين\n" +
                    "• فحص السائقين والمركبات\n" +
                    "• فحص المشاريع والزيارات الميدانية\n" +
                    "• تحديث الإحصائيات\n\n" +
                    "يرجى الانتظار...",
                    "تحديث شامل للنظام",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                // Force refresh data service (reload from files)
                await _dataService.RefreshAllDataAsync();

                // Load fresh data from all sources
                var sectors = await _dataService.GetSectorsAsync();
                var drivers = await _dataService.GetDriversAsync();
                var vehicles = await _dataService.GetVehiclesAsync();
                var projects = await _dataService.GetProjectsAsync();
                var fieldVisits = await _dataService.GetFieldVisitsAsync();
                var officers = await _dataService.GetOfficersAsync();

                // Store old values for comparison
                int oldSectors = SectorsCount;
                int oldDrivers = DriversCount;
                int oldVehicles = VehiclesCount;
                int oldProjects = ProjectsCount;
                int oldFieldVisits = FieldVisitsCount;

                // Update counts
                SectorsCount = sectors.Count;
                DriversCount = drivers.Count;
                VehiclesCount = vehicles.Count;
                ProjectsCount = projects.Count;
                FieldVisitsCount = fieldVisits.Count;

                // Update statistics display
                InitializeStatistics();

                // Prepare change summary
                var changes = new StringBuilder();
                changes.AppendLine("📊 تقرير التحديث الشامل:");
                changes.AppendLine();

                if (oldSectors != SectorsCount)
                    changes.AppendLine($"🏢 القطاعات: {oldSectors} ← {SectorsCount} ({GetChangeText(oldSectors, SectorsCount)})");
                else
                    changes.AppendLine($"🏢 القطاعات: {SectorsCount} (لا توجد تغييرات)");

                if (oldDrivers != DriversCount)
                    changes.AppendLine($"👤 السائقين: {oldDrivers} ← {DriversCount} ({GetChangeText(oldDrivers, DriversCount)})");
                else
                    changes.AppendLine($"👤 السائقين: {DriversCount} (لا توجد تغييرات)");

                if (oldVehicles != VehiclesCount)
                    changes.AppendLine($"🚗 السيارات: {oldVehicles} ← {VehiclesCount} ({GetChangeText(oldVehicles, VehiclesCount)})");
                else
                    changes.AppendLine($"🚗 السيارات: {VehiclesCount} (لا توجد تغييرات)");

                if (oldProjects != ProjectsCount)
                    changes.AppendLine($"📋 المشاريع: {oldProjects} ← {ProjectsCount} ({GetChangeText(oldProjects, ProjectsCount)})");
                else
                    changes.AppendLine($"📋 المشاريع: {ProjectsCount} (لا توجد تغييرات)");

                if (oldFieldVisits != FieldVisitsCount)
                    changes.AppendLine($"🗂️ الزيارات الميدانية: {oldFieldVisits} ← {FieldVisitsCount} ({GetChangeText(oldFieldVisits, FieldVisitsCount)})");
                else
                    changes.AppendLine($"🗂️ الزيارات الميدانية: {FieldVisitsCount} (لا توجد تغييرات)");

                changes.AppendLine();
                changes.AppendLine($"👥 إجمالي الموظفين: {officers.Count}");
                changes.AppendLine($"📅 آخر تحديث: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");

                // Show detailed success message
                System.Windows.MessageBox.Show(
                    $"✅ تم تحديث قاعدة البيانات بنجاح!\n\n{changes}",
                    "تم التحديث بنجاح",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"❌ حدث خطأ أثناء تحديث قاعدة البيانات:\n\n{ex.Message}\n\n" +
                    "تفاصيل الخطأ:\n" +
                    $"• النوع: {ex.GetType().Name}\n" +
                    $"• المصدر: {ex.Source}\n" +
                    $"• الوقت: {DateTime.Now:yyyy/MM/dd HH:mm:ss}",
                    "خطأ في التحديث",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }

            // Notify listeners that data has been refreshed
            DatabaseRefreshed?.Invoke();
        }

        private async void AddRealData()
        {
            try
            {
                System.Windows.MessageBox.Show(
                    "🌱 بدء إضافة البيانات الحقيقية...\n\n" +
                    "• إضافة الموظفين الحقيقيين\n" +
                    "• إضافة السائقين الحقيقيين\n" +
                    "• ربط البيانات بالقطاعات\n\n" +
                    "يرجى الانتظار...",
                    "إضافة البيانات الحقيقية",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);

                // إضافة البيانات الحقيقية
                var realSeeder = new RealDataSeeder();
                await realSeeder.SeedRealDataAsync();
                realSeeder.Dispose();

                // تحديث الإحصائيات
                await LoadStatisticsAsync();

                System.Windows.MessageBox.Show(
                    "✅ تم إضافة البيانات الحقيقية بنجاح!\n\n" +
                    $"📊 الإحصائيات الجديدة:\n" +
                    $"• القطاعات: {SectorsCount}\n" +
                    $"• الموظفين: {(await _dataService.GetOfficersAsync()).Count}\n" +
                    $"• السائقين: {DriversCount}\n" +
                    $"• السيارات: {VehiclesCount}",
                    "تم بنجاح",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"❌ حدث خطأ أثناء إضافة البيانات الحقيقية:\n\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }



        private async void ExecuteOpenOffers()
        {
            try
            {
                // الحصول على جميع السائقين النشطين
                var allDrivers = await _dataService.GetDriversAsync();
                var activeDrivers = new System.Collections.ObjectModel.ObservableCollection<Driver>(
                    allDrivers.Where(d => d.IsActive).ToList()
                );

                if (!activeDrivers.Any())
                {
                    System.Windows.MessageBox.Show(
                        "لا توجد سائقين نشطين في النظام",
                        "تحذير",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Warning);
                    return;
                }

                // فتح نافذة العروض
                var offersWindow = new Views.OffersWindow(activeDrivers, "001", 3, _dataService);
                var result = offersWindow.ShowDialog();

                if (result == true)
                {
                    var selectedOffers = offersWindow.GetSelectedOffers();
                    var winner = offersWindow.GetWinnerOffer();

                    if (selectedOffers?.Any() == true)
                    {
                        var offersText = string.Join(" | ", selectedOffers.Select(o => o.ToSaveString()));

                        System.Windows.MessageBox.Show(
                            $"✅ تم حفظ العروض بنجاح!\n\nالعروض المختارة:\n{offersText}" +
                            (winner != null ? $"\n\n🏆 الفائز: {winner.DriverName} - {winner.FormattedAmount}" : ""),
                            "نجح الحفظ",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"❌ خطأ في فتح نافذة العروض:\n\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        private string GetChangeText(int oldValue, int newValue)
        {
            if (newValue > oldValue)
                return $"+{newValue - oldValue} إضافة";
            else if (newValue < oldValue)
                return $"-{oldValue - newValue} حذف";
            else
                return "بدون تغيير";
        }




    }

    public class QuickAction
    {
        public string Title { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
    }

    public class StatisticCard
    {
        public string Title { get; set; } = string.Empty;
        public int Count { get; set; }
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
    }
}